<?php

declare(strict_types=1);

namespace app\api\controller;

use app\api\model\DataUser;
use app\api\model\Order as modelOrder;
use app\api\model\Product;
use app\model\OrderInfo;
use think\Request;
use think\admin\Controller;

class Order extends Controller
{
    /**
     * 获取用户订单列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $data = $this->_vali([
            'page.number' => '页码必须为数字',
            'limit.number' => '每页数量必须为数字',
            'status.number|in:0,1,2,3,4,5,6,7,8,9,10' => '状态必须为数字',
            'orderno.default' => '订单号不能为空',
            'id_number.default' => '身份证号',
            'mobile.default' => '手机号',
            'name.default' => '姓名',
        ]);

        $page = $data['page'] ?? 1;
        $limit = $data['limit'] ?? 20;

        $uid = request()->uid();
        // 构建查询条件
        $where = [
            'deleted' => 0
        ];

        $role = DataUser::mk()->where([
            'id' => $uid,
            'deleted' => 0,
            'status' => 1,
        ])->value('role');

        if (in_array($role, [1, 2])) {
            $where['uid'] = $uid;
        } elseif ($role == 3) {
            $where['buid'] = $uid;
        } else {
            $this->error('当前用户不可查看订单');
        }

        // 状态筛选
        if (isset($data['status']) && $data['status'] !== '') {
            $where['status'] = $data['status'];
        }

        if (isset($data['orderno']) && $data['orderno'] !== '') {
            $where['orderno'] = $data['orderno'];
        }
        if (isset($data['id_number']) && $data['id_number'] !== '') {
            $where['id_number'] = ['like', '%' . $data['id_number'] . '%'];
        }
        if (isset($data['mobile']) && $data['mobile'] !== '') {
            $where['mobile'] = $data['mobile'];
        }
        if (isset($data['name']) && $data['name'] !== '') {
            $where['name'] = ['like', '%' . $data['name'] . '%'];
        }

        // 查询订单列表
        $query = modelOrder::mk()
            ->where($where)
            ->field('id,orderno,name,apply_type,product_type,application_amount,loan_term,status,create_at')
            ->order('create_at desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();

        // 处理数据
        foreach ($list as &$item) {
            $item['apply_type_text'] = modelOrder::mk()->getApplyTypeText($item['apply_type']);
            $item['product_type_text'] = modelOrder::mk()->getProductTypeText($item['product_type']);
            $item['status_text'] = modelOrder::mk()->getStatusText($item['status']);
            // 预计收益金额 TODO
            $item['total_amount'] = $item['application_amount'] + $item['application_amount'] * 0.12;
        }

        $result = [
            'total_amount' => '15000', //TODO
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];

        $this->success('', $result);
    }


    /**
     * 保存新建的资源
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function addOrder(Request $request)
    {
        $data = $this->_vali([
            'product_id.require' => '产品id不能为空！',
            'apply_type.require' => '申请类型必填',
            'product_type.require' => '产品类型必填',
            'application_amount.require' => '申请额度必填',
            'loan_term.require' => '贷款期限必填',
            'id_card_front_img.require' => '身份证正面图片路径必填',
            'id_card_back_img.require' => '身份证反面图片路径必填',
            'name.require' => '借款人姓名必填',
            'gender.require' => '性别必填',
            'id_number.require' => '身份证号码必填',
            'mobile.require' => '手机号码必填',
            'email.require' => '电子邮箱必填',
            'hometown.require' => '籍贯必填',
            'marital_status.require' => '婚姻状况必填',
            'children_count.require' => '子女数目必填',
            'residence_type.require' => '居住类别必填',
            'address.require' => '住宅地址必填',
            'home_phone.require' => '住宅电话必填',
            'residence_years.require' => '居住年限必填',
            'has_car.require' => '是否有车必填',
            'has_car_loan.require' => '有无车贷必填',
            'has_house.require' => '是否有房必填',
            'has_house_loan.require' => '有无房贷必填',
            'company_name.require' => '单位名称必填',
            'department.require' => '任职部门必填',
            'position.require' => '职位必填',
            'company_address.require' => '单位地址必填',
            'company_type.require' => '单位性质必填',
            'company_phone.require' => '单位电话必填',
            'work_years.require' => '现单位工作年限必填',
            'job_nature.require' => '工作性质必填',
            'industry.require' => '行业类别必填',
            'company_size.require' => '公司规模必填',
            'monthly_income.require' => '月收入必填',
            'family_contact_name.require' => '直系亲属姓名必填',
            'family_contact_gender.require' => '直系亲属性别必填',
            'family_contact_address.require' => '直系亲属现居住地址必填',
            'family_contact_relation.require' => '直系亲属与申请人关系必填',
            'family_contact_phone.require' => '直系亲属联系电话必填',
            'family_contact_company.require' => '直系亲属任职单位必填',
            'family_contact_know_loan.require' => '直系亲属是否知悉贷款必填',
            'other_contact_name.require' => '其他联系人姓名必填',
            'other_contact_gender.require' => '其他联系人性别必填',
            'other_contact_address.require' => '其他联系人现居住地址必填',
            'other_contact_relation.require' => '其他联系人与申请人关系必填',
            'other_contact_phone.require' => '其他联系人联系电话必填',
            'other_contact_company.require' => '其他联系人任职单位必填',
            'other_contact_know_loan.require' => '其他联系人是否知悉贷款必填',
            'housing_fund_proof.require' => '公积金截据图+缴存明细必填',
            'social_insurance_proof.require' => '社保清单必填',
            'tax_record_proof.require' => '个税清单必填',
            'education_proof.require' => '学信网学历截图必填',
            'address_proof.require' => '住址认证截图必填',
            'other_asset_proof.require' => '其他资产证明必填',
            'payment_bank_card1.require' => '缴款银行卡1卡号必填',
            'payment_bank_card1_imgs.require' => '缴款银行卡1照片必填',
            'payment_bank_card2.require' => '缴款银行卡2卡号必填',
            'payment_bank_card2_imgs.require' => '缴款银行卡2照片必填',
            'repayment_bank_card.require' => '还款银行卡1(工资卡)卡号必填',
            'repayment_bank_card_imgs.require' => '还款银行卡1(工资卡)照片必填',
        ]);
        //业务员提交的，所以当前用户是bid
        $data['buid'] = request()->id();
        //还需要获取用户的角色
        $role = DataUser::mk()->where([
            'id' => $data['buid'],
            'status' => 1,
        ])->where(['deleted' => 0])->value('role');

        if (!$role || !in_array($role, [3])) {
            $this->error('当前用户不可提交');
        }

        $productInfo = Product::mk()->where([
            'id' => $data['product_id'],
            'status' => 1,
        ])->where(['deleted' => 0])->findOrEmpty();

        if (is_null($productInfo)) {
            $this->error('产品不存在');
        }
        
        $data['product_name'] = $productInfo['name'];

        //用户不能重复申请已经申请过的项目，如果该项目已经审核通过了，可以申请
        $orderInfo =  OrderInfo::mk()->where([
            'product_id' => $data['product_id'],
            'id_number' => $data['id_number'],
            'product_type' => $data['product_type'],
            'apply_type' => $data['apply_type'],
        ])->select();

        if (!$orderInfo->isEmpty()) {
            // 提取所有订单ID（假设主键为id）
            $orderIds = $orderInfo->column('id');

            // 查询这些订单中是否存在状态为STATUS_SUBMIT的记录
            $exists = modelOrder::mk()
                ->where('id', 'in', $orderIds)
                ->where('status', modelOrder::STATUS_SUBMIT)
                ->count();

            if ($exists > 0) {
                $this->error('当前用户已经提交过该申请');
            }
        }

        // 插入订单信息
        do {
            $orderno = modelOrder::mk()->generateOrderNumber('DKX', $data['buid']);
            $count = modelOrder::mk()->where([
                'orderno' => $orderno,
            ])->count();
        } while ($count > 0);

        try {
            // 开启事务
            modelOrder::mk()->startTrans();

            $data['orderno'] = $orderno;

            $ret = modelOrder::mk()->addOrder($data);
            $rets = OrderInfo::mk()->addOrderInfo($data);

            if ($ret > 0) {
                $data['id'] = modelOrder::mk()->getLastInsID();
                event('AddOrderSuccess', $data);
            }
            // 提交事务
            modelOrder::mk()->commit();
        } catch (\Exception $e) {
            // 回滚事务
            modelOrder::mk()->rollback();
            $this->error('提交失败');
        }

        $this->success('操作成功', ['orderid' => $data['id']]);
    }
    //更新订单信息
    public function updateOrder()
    {
        // 先验证基本字段
        $basicData = $this->_vali([
            'id.require' => '订单id不能为空！',
        ]);

        $orderId = $basicData['id'];

        //业务员提交的，所以当前用户是bid
        $uid = request()->id();
        //还需要获取用户的角色
        $role = DataUser::mk()->where([
            'id' => $uid,
            'status' => 1,
        ])->where(['deleted' => 0])->value('role');

        if (!$role || !in_array($role, [3])) {
            $this->error('当前用户不可提交');
        }

        // 获取订单信息，包括rejected_fields
        $orderinfo = modelOrder::mk()->where(['id' => $orderId, 'status' => 1])->findOrEmpty();
        if (is_null($orderinfo)) {
            $this->error('订单不存在或状态不正确');
        }

        $orderinfo = OrderInfo::mk()->where(['id' => $orderId])->findOrEmpty();
        

        // 解析rejected_fields字段
        $rejectedFields = [];
        if (!empty($orderinfo['rejected_fields'])) {
            $rejectedFields = json_decode($orderinfo['rejected_fields'], true);
            if (!is_array($rejectedFields)) {
                $rejectedFields = [];
            }
        }

        // 如果没有驳回字段，不允许更新
        if (empty($rejectedFields)) {
            $this->error('该订单没有被驳回的字段，无需更新');
        }

        // 定义所有可能的字段验证规则
        $allValidationRules = [
            // 'product_id' => '产品id不能为空！',
            'apply_type' => '申请类型必填',
            'product_type' => '产品类型必填',
            'application_amount' => '申请额度必填',
            'loan_term' => '贷款期限必填',
            'id_card_front_img' => '身份证正面图片路径必填',
            'id_card_back_img' => '身份证反面图片路径必填',
            'name' => '借款人姓名必填',
            'gender' => '性别必填',
            'id_number' => '身份证号码必填',
            'mobile' => '手机号码必填',
            'email' => '电子邮箱必填',
            'hometown' => '籍贯必填',
            'marital_status' => '婚姻状况必填',
            'children_count' => '子女数目必填',
            'residence_type' => '居住类别必填',
            'address' => '住宅地址必填',
            'home_phone' => '住宅电话必填',
            'residence_years' => '居住年限必填',
            'has_car' => '是否有车必填',
            'has_car_loan' => '有无车贷必填',
            'has_house' => '是否有房必填',
            'has_house_loan' => '有无房贷必填',
            'company_name' => '单位名称必填',
            'department' => '任职部门必填',
            'position' => '职位必填',
            'company_address' => '单位地址必填',
            'company_type' => '单位性质必填',
            'company_phone' => '单位电话必填',
            'work_years' => '现单位工作年限必填',
            'job_nature' => '工作性质必填',
            'industry' => '行业类别必填',
            'company_size' => '公司规模必填',
            'monthly_income' => '月收入必填',
            'family_contact_name' => '直系亲属姓名必填',
            'family_contact_gender' => '直系亲属性别必填',
            'family_contact_address' => '直系亲属现居住地址必填',
            'family_contact_relation' => '直系亲属与申请人关系必填',
            'family_contact_phone' => '直系亲属联系电话必填',
            'family_contact_company' => '直系亲属任职单位必填',
            'family_contact_know_loan' => '直系亲属是否知悉贷款必填',
            'other_contact_name' => '其他联系人姓名必填',
            'other_contact_gender' => '其他联系人性别必填',
            'other_contact_address' => '其他联系人现居住地址必填',
            'other_contact_relation' => '其他联系人与申请人关系必填',
            'other_contact_phone' => '其他联系人联系电话必填',
            'other_contact_company' => '其他联系人任职单位必填',
            'other_contact_know_loan' => '其他联系人是否知悉贷款必填',
            'housing_fund_proof' => '公积金截据图+缴存明细必填',
            'social_insurance_proof' => '社保清单必填',
            'tax_record_proof' => '个税清单必填',
            'education_proof' => '学信网学历截图必填',
            'address_proof' => '住址认证截图必填',
            'other_asset_proof' => '其他资产证明必填',
            'payment_bank_card1' => '缴款银行卡1卡号必填',
            'payment_bank_card1_imgs' => '缴款银行卡1照片必填',
            'payment_bank_card2' => '缴款银行卡2卡号必填',
            'payment_bank_card2_imgs' => '缴款银行卡2照片必填',
            'repayment_bank_card' => '还款银行卡1(工资卡)卡号必填',
            'repayment_bank_card_imgs' => '还款银行卡1(工资卡)照片必填',
            'loan_purpose' => '借款目的必填',
        ];

        // 构建只针对被驳回字段的验证规则
        $validationRules = ['id.require' => '订单id不能为空！'];
        foreach ($rejectedFields as $field) {
            if (isset($allValidationRules[$field])) {
                $validationRules[$field . '.require'] = $allValidationRules[$field];
            }
        }

        // 验证提交的数据
        $data = $this->_vali($validationRules);

        $productInfo = Product::mk()->where([
            'id' => $orderinfo['product_id'],
            'status' => 1,
        ])->where(['deleted' => 0])->findOrEmpty();

        if (is_null($productInfo)) {
            $this->error('产品不存在');
        }

        // 只更新被驳回的字段
        $updateData = [];
        foreach ($rejectedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        // 添加更新时间
        $updateData['update_at'] = date('Y-m-d H:i:s');

        // 执行更新
        $ret = $orderinfo->strict(false)->update($updateData);

        if ($ret) {
            $data['orderno'] = $orderinfo['orderno'];
            $data['id'] = $orderId;
            //提交订单成功后的事件
            event('UpdateOrderSuccess', $data);

            $this->success('提交成功，已更新被驳回的字段', ['orderid' => $orderId, 'updated_fields' => array_keys($updateData)]);
        } else {
            $this->error('提交失败');
        }
    }

    /**
     * 显示指定的资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function read()
    {
        $data = $this->_vali([
            'orderid.require' => '订单id不能为空！',
        ]);

        $ret = modelOrder::mk()->getOrder(['id' => $data['orderid']], '*');

        if (!$ret->isEmpty()) {
            $this->success('操作成功', $ret);
        } else {
            $this->error('操作失败,数据未找到');
        }
    }

    //展示签约页面
    public function showSignPage()
    {
        // 验证参数
        $data = $this->_vali([
            'orderid.require|number' => '订单ID不能为空且必须为数字！',
        ]);

        // 获取订单详情头部
        $orderDetail = modelOrder::mk()->getOrderSign($data['orderid']);

        if (empty($orderDetail)) {
            $this->error('订单不存在或已被删除');
        }

        //需要展示
        $result = [
            'order_detail' => $orderDetail,
        ];

        $this->success('', $result);
    }

    /**
     * 查看订单还款周期
     * 用于放款完成后查看还款计划
     */
    public function getRepaymentSchedule()
    {
        // 验证参数
        $data = $this->_vali([
            'order_id.require|number' => '订单ID不能为空且必须为数字！',
        ]);

        $orderId = $data['order_id'];
        $uid = request()->id();

        // 验证用户权限
        $role = DataUser::mk()->where([
            'id' => $uid,
            'deleted' => 0,
            'status' => 1,
        ])->value('role');

        if (!$role || !in_array($role, [1, 2, 3])) {
            $this->error('当前用户无权限查看还款计划');
        }

        // 验证订单存在且已放款
        $orderInfo = modelOrder::mk()->where([
            'id' => $orderId,
            'deleted' => 0
        ])->findOrEmpty();

        if (is_null($orderInfo)) {
            $this->error('订单不存在');
        }

        // 检查订单状态是否为已放款（假设状态6为已放款）
        if ($orderInfo['status'] < 6) {
            $this->error('订单尚未放款，无法查看还款计划');
        }

        // 根据用户角色验证订单权限
        if (in_array($role, [1, 2]) && $orderInfo['uid'] != $uid) {
            $this->error('无权限查看此订单的还款计划');
        } elseif ($role == 3 && $orderInfo['buid'] != $uid) {
            $this->error('无权限查看此订单的还款计划');
        }

        // 查询还款计划
        $repaymentList = $this->getRepaymentList($orderId);

        if (empty($repaymentList)) {
            $this->error('暂无还款计划数据');
        }

        // 计算统计信息
        $statistics = $this->calculateRepaymentStatistics($repaymentList);

        $result = [
            'order_info' => [
                'id' => $orderInfo['id'],
                'orderno' => $orderInfo['orderno'],
                'name' => $orderInfo['name'],
                'application_amount' => $orderInfo['application_amount'],
                'approved_amount' => $orderInfo['approved_amount'],
                'loan_term' => $orderInfo['loan_term'],
                'status' => $orderInfo['status'],
                'status_text' => modelOrder::mk()->getStatusText($orderInfo['status'])
            ],
            'repayment_list' => $repaymentList,
            'statistics' => $statistics
        ];

        $this->success('获取还款计划成功', $result);
    }

    /**
     * 获取还款计划列表
     * @param int $orderId 订单ID
     * @return array
     */
    private function getRepaymentList($orderId)
    {
        // 这里需要根据你的dk_repayment表结构来查询
        // 假设表结构包含：id, order_id, uid, period, amount, interest, principal, repayment_date, status, paid_date等字段

        try {
            // 使用原生查询或者模型查询dk_repayment表
            $repaymentData = \think\facade\Db::table('dk_repayment')
                ->where('order_id', $orderId)
                ->order('period asc')
                ->select()
                ->toArray();

            // 处理数据格式
            $repaymentList = [];
            foreach ($repaymentData as $item) {
                $repaymentList[] = [
                    'id' => $item['id'],
                    'period' => $item['period'], // 期数
                    'amount' => $item['amount'], // 还款金额
                    'interest' => $item['interest'] ?? 0, // 利息
                    'principal' => $item['principal'] ?? 0, // 本金
                    'repayment_date' => $item['repayment_date'], // 应还日期
                    'status' => $item['status'], // 还款状态 0-未还 1-已还
                    'status_text' => $item['status'] == 1 ? '已还款' : '未还款',
                    'paid_date' => $item['paid_date'] ?? '', // 实际还款日期
                    'overdue_days' => $this->calculateOverdueDays($item['repayment_date'], $item['status']), // 逾期天数
                ];
            }

            return $repaymentList;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 计算还款统计信息
     * @param array $repaymentList 还款列表
     * @return array
     */
    private function calculateRepaymentStatistics($repaymentList)
    {
        $totalAmount = 0; // 总还款金额
        $paidAmount = 0; // 已还金额
        $unpaidAmount = 0; // 未还金额
        $totalPeriods = count($repaymentList); // 总期数
        $paidPeriods = 0; // 已还期数
        $overduePeriods = 0; // 逾期期数

        foreach ($repaymentList as $item) {
            $totalAmount += $item['amount'];

            if ($item['status'] == 1) {
                $paidAmount += $item['amount'];
                $paidPeriods++;
            } else {
                $unpaidAmount += $item['amount'];
                if ($item['overdue_days'] > 0) {
                    $overduePeriods++;
                }
            }
        }

        return [
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount,
            'unpaid_amount' => $unpaidAmount,
            'total_periods' => $totalPeriods,
            'paid_periods' => $paidPeriods,
            'unpaid_periods' => $totalPeriods - $paidPeriods,
            'overdue_periods' => $overduePeriods,
            'completion_rate' => $totalPeriods > 0 ? round(($paidPeriods / $totalPeriods) * 100, 2) : 0
        ];
    }

    /**
     * 计算逾期天数
     * @param string $repaymentDate 应还日期
     * @param int $status 还款状态
     * @return int
     */
    private function calculateOverdueDays($repaymentDate, $status)
    {
        // 如果已还款，不计算逾期
        if ($status == 1) {
            return 0;
        }

        $today = date('Y-m-d');
        $repaymentDate = date('Y-m-d', strtotime($repaymentDate));

        if ($today > $repaymentDate) {
            $diff = strtotime($today) - strtotime($repaymentDate);
            return floor($diff / (24 * 60 * 60));
        }

        return 0;
    }
}
