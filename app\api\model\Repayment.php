<?php
declare (strict_types = 1);

namespace app\api\model;

use think\admin\Model;
use app\api\model\Order ;
/**
 * 还款API模型
 * Class Repayment
 * @package app\api\model
 */
class Repayment extends Model
{
    protected $table = 'dk_repayment';

    /**
     * 状态文本映射
     */
    public static $statusMap = [
        0 => '待还款',
        1 => '已还款',
        2 => '逾期',
        3 => '提前还清'
    ];

    /**
     * 获取用户还款列表
     * @param int $uid
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getUserRepaymentList($uid, $page = 1, $limit = 20)
    {
        $where = [
            ['uid', '=', $uid],
            ['deleted', '=', 0]
        ];
        // 查询订单列表
        $query = Order::mk()
            ->where($where)
            ->field('id,orderno,name,apply_type,product_type,application_amount,loan_term,status,create_at,total_surplus,total_surplus_num,month_should_money,total_money,month_amount')
            ->order('create_at desc');

        $total = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();

        // 处理数据
        foreach ($list as &$item) {
            $item['apply_type_text'] = Order::mk()->getApplyTypeText($item['apply_type']);
            $item['product_type_text'] = Order::mk()->getProductTypeText($item['product_type']);
            $item['status_text'] = Order::mk()->getStatusText($item['status']);
        }

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    //获取还款详情接口
    public static function getRepaymentDetail($id, $uid = 0)
    {
        // 获取订单详情
        $orderDetail = Order::mk()->getRepayOrderTop($id);
        
        if (empty($orderDetail)) {
            return [];
        }

        // 获取还款详情
        $repaymentList = self::mk()
            ->where('order_id', $id)
            ->where('deleted', 0)
            ->order('period asc')
            ->select()
            ->toArray();

        // 处理数据 
        foreach ($repaymentList as &$item) {
            $item['status_text'] = self::$statusMap[$item['status']] ?? '未知状态';
            $item['total_amount'] = number_format((float)$item['total_amount']??0, 2);
            $item['principal'] = number_format((float)$item['principal']??0, 2);
            $item['interest'] = number_format((float)$item['interest']??0, 2);
        }
        return [
            'order_detail' => $orderDetail,
            'repayment_list' => $repaymentList,
        ];

    }

    /**
     * 提前还款
     * @param int $id
     * @param int $uid
     * @param string $remark
     * @return bool
     */
    public static function earlyRepayment($id, $uid, $remark = '', $img = '')
    {
        $repayment = self::mk()
            ->where('id', $id)
            ->where('uid', $uid)
            ->where('status', 0)
            ->where('deleted', 0)
            ->find();

        if (!$repayment) {
            return false;
        }

        $updateData = [
            'status' => 3, // 提前还清
            'actual_repayment_date' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
            'img' => $img,
        ];

        if ($remark) {
            $updateData['remark'] = $remark;
        }

        return $repayment->save($updateData);
    }

    // /**
    //  * 获取用户还款统计
    //  * @param int $uid
    //  * @return array
    //  */
    // public static function getUserRepaymentStats($uid)
    // {
    //     $stats = self::mk()
    //         ->where('uid', $uid)
    //         ->where('deleted', 0)
    //         ->field('status, count(*) as count, sum(total_amount) as total_amount')
    //         ->group('status')
    //         ->select()
    //         ->toArray();

    //     $result = [
    //         'total_count' => 0,
    //         'total_amount' => 0,
    //         'pending_count' => 0,
    //         'pending_amount' => 0,
    //         'completed_count' => 0,
    //         'completed_amount' => 0,
    //         'overdue_count' => 0,
    //         'overdue_amount' => 0,
    //     ];

    //     foreach ($stats as $stat) {
    //         $result['total_count'] += $stat['count'];
    //         $result['total_amount'] += $stat['total_amount'];

    //         switch ($stat['status']) {
    //             case 0: // 待还款
    //                 $result['pending_count'] = $stat['count'];
    //                 $result['pending_amount'] = $stat['total_amount'];
    //                 break;
    //             case 1: // 已还款
    //             case 3: // 提前还清
    //                 $result['completed_count'] += $stat['count'];
    //                 $result['completed_amount'] += $stat['total_amount'];
    //                 break;
    //             case 2: // 逾期
    //                 $result['overdue_count'] = $stat['count'];
    //                 $result['overdue_amount'] = $stat['total_amount'];
    //                 break;
    //         }
    //     }

    //     // 格式化金额
    //     $result['total_amount'] = number_format((float)$result['total_amount'], 2);
    //     $result['pending_amount'] = number_format((float)$result['pending_amount'], 2);
    //     $result['completed_amount'] = number_format((float)$result['completed_amount'], 2);
    //     $result['overdue_amount'] = number_format((float)$result['overdue_amount'], 2);

    //     return $result;
    // }
}
