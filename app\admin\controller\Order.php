<?php

// +----------------------------------------------------------------------
// | Admin Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-admin
// | github 代码仓库：https://github.com/zoujingli/think-plugs-admin
// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\model\Order as ModelOrder;
use app\data\model\DataUser;
use think\admin\Controller;

use think\admin\helper\QueryHelper;
use think\admin\model\SystemAuth;
use think\admin\model\SystemBase;
use think\admin\model\SystemUser;
use think\admin\service\AdminService;


/**
 * 订单管理
 * @class User

 * @package app\admin\controller
 */
class Order extends Controller
{
    /**
     * 订单列表
     * @auth true
     * @menu true
     *
     * @return void
     */
    public function index()
    {
        ModelOrder::mQuery()->like('name,mobile')->equal('status')->dateBetween('create_time')->layTable(function (){
            //前置html操作
            $this->title = '订单管理';
        }, function (QueryHelper $query) {
            //后置html操作
        });

    }
      /**
     * 列表数据处理
     * @param array $data
     * @throws \Exception
     */
    protected function _index_page_filter(array &$data)
    {
        foreach ($data as &$vo) {
            $info = DataUser::mk()->where(['id' => $vo['buid']])->field('username,phone')->find();
            $vo['user_name'] = $info['username']?$info['username']:$info['phone'];
        }
    }
    /**
     * 添加系统用户
     * @auth true
     */
    public function add()
    {
        ModelOrder::mForm('form');
    }

    /**
     * 编辑系统用户
     * @auth true
     */
    public function edit()
    {
        ModelOrder::mForm('form');
    }

    public function detail() {
        if ($this->request->isGet()) {
            $map = $this->_vali(['orderno.require' => '订单编号不能为空哦！']);
            $list = ModelOrder::mk()->where($map)->find();
            if (empty($list)) $this->error('无效的订单数据，请稍候再试！');

            // 处理驳回字段数据
            $rejectedFields = [];
            if (!empty($list['rejected_fields'])) {
                $decodedFields = json_decode($list['rejected_fields'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedFields)) {
                    $rejectedFields = $decodedFields;
                }
            }

            $this->vo = $list;
            $this->rejectedFields = $rejectedFields;
            $this->fetch();
        } else {
            $data = $this->_vali([
                'id.require' => '订单ID不能为空！',
                'status.require' => '审核状态不能为空！',
                'status.in:1,2' => '审核状态数值异常！',
                'remark.default' => '',
                'rejected_fields.default' => '',
                'approved_amount.default' => 0,
            ]);

            // 处理审核操作
            $status = $data['status'] ?? 0; // 0待审核,1审核驳回,2审核通过
            $remark = $data['remark'] ?? '';
            $approved_amount = $data['approved_amount'] ?? 0; // 审批额度
            $rejected_fields = $data['rejected_fields'] ?: ''; // 驳回字段

            // 验证审批额度
            if ($status == 2 && (!$approved_amount || $approved_amount <= 0)) {
                $this->error('审核通过时必须填写审批额度！');
            }

            // 验证驳回字段
            if ($status == 1 && !empty($rejected_fields)) {
                // 验证JSON格式
                $decodedFields = json_decode($rejected_fields, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error('驳回字段格式错误！');
                }
            }

            $map = ['id' => $data['id']];

            // 准备更新数据
            $updateData = [
                'status' => $status, // 1审核驳回，2审核通过
                'audit_remark' => $remark,
                'audit_time' => date('Y-m-d H:i:s')
            ];

            // 如果是审核通过，添加审批额度
            if ($status == 2) {
                $updateData['approved_amount'] = $approved_amount;
                // 清空驳回字段
                $updateData['rejected_fields'] = '';
            }

            // 如果是审核驳回，保存驳回字段
            if ($status == 1 && !empty($rejected_fields)) {
                $updateData['rejected_fields'] = $rejected_fields;
            }
            // 更新订单状态
            $result = ModelOrder::mk()->where($map)->update($updateData);

            if ($result) {
                if($status == 1){
                    event('OrderReject', ['id' => $data['id']]);
                }else{
                    event('PassOrderSuccess', ['id' => $data['id']]);
                }
                $this->success($status == 2 ? '审核通过成功！' : '审核驳回成功！');
            } else {
                $this->error('审核操作失败！');
            }
        }
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function _form_filter(array &$data)
    {
        // if ($this->request->isPost()) {
        //     // 检查资料是否完整
        //     empty($data['username']) && $this->error('登录账号不能为空！');
        //     if ($data['username'] !== AdminService::getSuperName()) {
        //         empty($data['authorize']) && $this->error('未配置权限！');
        //     }
        //     // 处理上传的权限格式
        //     $data['authorize'] = arr2str($data['authorize'] ?? []);
        //     if (empty($data['id'])) {
        //         // 检查账号是否重复
        //         $map = ['username' => $data['username'], 'is_deleted' => 0];
        //         if (SystemUser::mk()->where($map)->count() > 0) {
        //             $this->error("账号已经存在，请使用其它账号！");
        //         }
        //         // 新添加的用户密码与账号相同
        //         $data['password'] = md5($data['username']);
        //     } else {
        //         unset($data['username']);
        //     }
        // } else {
        //     // 权限绑定处理
        //     $data['authorize'] = str2arr($data['authorize'] ?? '');
        //     $this->auths = SystemAuth::items();
        //     $this->bases = SystemBase::items('身份权限');
        //     $this->super = AdminService::getSuperName();
        // }
    }
    /**
     * 状态变更处理
     * @auth true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function _save_result()
    {
        $this->_form_result(true);
    }

    /**
     * 删除结果处理
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function _delete_result()
    {
        $this->_form_result(true);
    }

    /**
     * 放款管理
     * @auth true
     */
    public function loan()
    {
        if ($this->request->isGet()) {
            $data = $this->_vali(['id.require' => '订单ID不能为空！']);
            $order = ModelOrder::mk()->where('id', $data['id'])->find();
            if (!$order) {
                $this->error('订单不存在');
            }
            if ($order['status'] != 4) {
                $this->error('订单状态不正确，只有面签通过的订单才能进行放款操作');
            }
            $this->order = $order;
            $this->fetch();
        } else {
            $data = $this->_vali([
                'id.require' => '订单ID不能为空！',
                'loan_amount.float|egt:0' => '放款金额必须大于等于0！',
                'loan_screenshot.default' => '',
                'loan_remark.default' => '',
                'loan_rate.require' => '放款利率不能为空',
                'monthly_repayment_day.require|between:1,31' => '每月还款日必须在1-31之间！',
                'daily_penalty_rate.require|float|egt:0|elt:1' => '日罚息利率必须在0-1之间！',
                'daily_overdue_rate.require|float|egt:0|elt:1' => '日逾期利率必须在0-1之间！',
                'profit_sharing_amount.require|float|egt:0' => '分润金额必须大于等于0！',
            ]);

            // 验证订单状态
            $order = ModelOrder::mk()->where('id', $data['id'])->find();
            if (!$order || $order['status'] != 4) {
                $this->error('订单状态不正确');
            }
            if ($data['loan_amount'] <= 0) {
                $this->error('放款金额错误');
            }

            // 格式化数据
            $data['daily_penalty_rate'] = number_format((float)$data['daily_penalty_rate'], 6, '.', '');
            $data['daily_overdue_rate'] = number_format((float)$data['daily_overdue_rate'], 6, '.', '');
            $data['profit_sharing_amount'] = number_format((float)$data['profit_sharing_amount'], 2, '.', '');

            $updateData = [
                'status' => 7, // 放款完成
                'loan_amount' => $data['loan_amount'],
                'loan_screenshot' => $data['loan_screenshot'],
                'loan_remark' => $data['loan_remark'],
                'loan_time' => date('Y-m-d H:i:s'),
                'monthly_repayment_day' => $data['monthly_repayment_day'],
                'daily_penalty_rate' => $data['daily_penalty_rate'],
                'daily_overdue_rate' => $data['daily_overdue_rate'],
                'profit_sharing_amount' => $data['profit_sharing_amount']
            ];

            $result = ModelOrder::mk()->where('id', $data['id'])->save($updateData);
            if ($result) {
                event('LoanOrderSuccess', ['id' => $data['id']]);
                sysoplog('订单管理', "订单[{$data['id']}]放款完成，金额：{$data['loan_amount']}");
                $this->success('放款操作成功！');
            } else {
                $this->error('放款操作失败！');
            }
        }
    }

    /**
     * 关闭订单
     * @auth true
     */
    public function close()
    {
        if ($this->request->isPost()) {
            $data = $this->_vali([
                'id.require' => '订单ID不能为空！',
                'close_reason.default' => '',
            ]);

            // 获取订单信息
            $order = ModelOrder::mk()->where('id', $data['id'])->find();
            if (!$order) {
                $this->error('订单不存在！');
            }

            // 验证订单状态：只能关闭状态为1(审核驳回)、5(面签驳回)、9(还款完成)的订单
            if (!in_array($order['status'], [1, 5, 9])) {
                $statusMap = [
                    0 => '提交申请表',
                    1 => '审核驳回',
                    2 => '审核通过待面签',
                    4 => '面签通过',
                    5 => '面签驳回',
                    6 => '放款中',
                    7 => '放款完成',
                    8 => '还款中',
                    9 => '还款完成',
                    10 => '结清'
                ];
                $currentStatus = $statusMap[$order['status']] ?? '未知状态';
                $this->error("当前订单状态为【{$currentStatus}】，只能关闭状态为【审核驳回】、【面签驳回】、【还款完成】的订单！");
            }

            // 更新订单状态为结清(10)
            $updateData = [
                'status' => 10, // 结清
                'close_reason' => $data['close_reason'],
                'close_time' => date('Y-m-d H:i:s')
            ];

            $result = ModelOrder::mk()->where('id', $data['id'])->update($updateData);
            if ($result) {
                event('OrderClose', ['id' => $data['id']]); // 触发关闭事件
                sysoplog('订单管理', "订单[{$order['orderno']}]已关闭，关闭原因：{$data['close_reason']}");
                $this->success('订单关闭成功！');
            } else {
                $this->error('订单关闭失败！');
            }
        }
    }



    /**
     * 表单结果处理
     * @param boolean $state
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function _form_result(bool $state)
    {
        // if ($state) {
        //     $isasc = input('old_number', 0) <= input('number', 0);
        //     $order = $isasc ? 'number asc,utime asc' : 'number asc,utime desc';
        //     foreach (ModelOrder::mk()->order($order)->select() as $number => $Obj) {
        //         $Obj->save(['number' => $number]);
        //     }
        // }
    }

}